<template>
  <q-page class="q-pa-md">
    <div v-if="loading" class="text-center q-pa-lg">
      <q-spinner size="lg" color="primary" />
      <div class="q-mt-md">Loading assessment...</div>
    </div>
    <div v-else>
      <!-- <BlockCreator :blocks="blocks" :assessmentId="assessmentId" type="evaluate" /> -->
      <!-- EvaluateEditorView.vue -->
      <BlockCreator :assessmentId="assessmentId" type="evaluate" />
    </div>
  </q-page>
</template>

<script setup lang="ts">
import BlockCreator from 'src/components/common/blocks/BlockCreator.vue';
import { computed, onMounted, watch, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useBlockCreatorStore } from 'src/stores/block_creator';

const blockCreatorStore = useBlockCreatorStore();
const route = useRoute();

const assessmentId = computed(() => blockCreatorStore.currentAssessment?.id || null);

// const blocks = computed(() => {
//   const assessmentBlocks = blockCreatorStore.currentAssessment?.itemBlocks;

//   if (assessmentBlocks && assessmentBlocks.length > 0) {
//     return assessmentBlocks;
//   } else {
//     return [];
//   }
// });
// ใน script setup
const loading = ref(true);

async function fetchAssessment() {
  loading.value = true;
  const id = route.params.id;
  if (id) {
    await blockCreatorStore.fetchAssessmentById(Number(id));
  }
  loading.value = false;
}

onMounted(fetchAssessment);

watch(
  () => route.params.id,
  async (newId, oldId) => {
    if (newId && newId !== oldId) {
      await fetchAssessment();
    }
  },
);
</script>
<style scoped></style>
