<template>
  <q-card
    v-if="category != 'HEADER' && matchedItem"
    class="q-pa-md q-ma-md evaluate-get"
    :class="{ 'border-error': isInvalid }"
  >
    <div class="row q-ma-md">
      <q-markdown class="title">
        {{ matchedItem?.questions?.[0]?.questionText || 'ไม่พบคำถาม' }}
      </q-markdown>
      <div class="required">*</div>
    </div>
    <div v-if="matchedItem.questions?.[0]?.imagePath" class="row justify-center q-ma-md">
      <q-img
        :src="matchedItem.questions?.[0]?.imagePath"
        fit="scale-down"
        :ratio="1"
        class="centered-image"
        :width="
          matchedItem.questions?.[0]?.imageWidth
            ? matchedItem.questions[0].imageWidth + 'px'
            : undefined
        "
        :height="
          matchedItem.questions?.[0]?.imageHeight
            ? matchedItem.questions[0].imageHeight + 'px'
            : '200px'
        "
      />
    </div>

    <div class="row q-ma-md">
      <div v-if="category === 'RADIO'" class="group font-size">
        <q-radio
          :disable="isPreview"
          v-model="selectedAnswer"
          v-for="choice in matchedItem?.options"
          :key="choice.id ?? choice.optionText"
          :val="String(choice.id)"
          color="primary"
          @update:model-value="onRadioChange"
        >
          <div class="row items-center q-gutter-sm" v-if="choice.imagePath">
            <span>{{ choice.optionText }}</span>
            <q-img
              v-if="choice.imagePath"
              :src="choice.imagePath"
              style="width: 100px; height: 100px"
              fit="contain"
            />
          </div>
        </q-radio>
      </div>

      <div v-else-if="category === 'CHECKBOX'" class="group font-size">
        <q-checkbox
          v-for="choice in matchedItem?.options"
          :key="choice.id"
          :val="String(choice.id)"
          v-model="selectedAnswers"
          :label="choice.optionText"
          color="primary"
          :disable="isPreview"
          @update:model-value="onCheckboxChange"
        />
      </div>

      <div v-else-if="category === 'TEXTFIELD'">
        <q-input
          :disable="isPreview"
          v-model="textAnswer"
          dense
          placeholder="คำตอบ..."
          style="min-width: 400px"
          class="font-size"
          @blur="emitAnswer"
        />
      </div>

      <div v-else-if="category === 'GRID'" class="grid-choice">
        <q-table flat bordered :rows="rows" :columns="columns" row-key="id" hide-bottom>
          <template v-slot:body-cell="props">
            <q-td :props="props">
              <template v-if="props.col.name === 'question'">
                {{ props.row.question }}
              </template>
              <template v-else>
                <q-radio
                  :disable="isPreview"
                  :val="Number(props.col.name.replace('choice_', ''))"
                  v-model="gridAnswers[props.row.id]"
                  size="sm"
                  color="primary"
                  @update:model-value="() => (isEdit = true)"
                />
              </template>
            </q-td>
          </template>
        </q-table>
      </div>
      <div v-else-if="category === 'UPLOAD'">
        <div class="q-mb-md" style="color: #9d9d9d">
          {{
            'อัปโหลด ' +
            MAX_SIZE_MB / (1024 * 1024) +
            ' MB' +
            ' ชนิดไฟล์ ' +
            matchedItem?.questions?.[0]?.acceptFile +
            ' มากสุด ' +
            matchedItem?.questions?.[0]?.uploadLimit
          }}
        </div>
        <!-- แสดงไฟล์ด้วย q-chip -->
        <div class="q-mt-md row q-gutter-sm">
          <q-chip
            v-for="(file, index) in files"
            :key="index"
            removable
            @remove="removeFile(index)"
            class="q-ma-xs"
            style="max-width: 220px"
            tag="a"
            :download="file.name"
            target="_blank"
          >
            <q-avatar color="red-5" text-color="white">
              <q-icon name="image" />
            </q-avatar>

            <span
              class="ellipsis"
              style="
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: inline-block;
                vertical-align: middle;
                max-width: 140px;
              "
            >
              <a
                :href="file.webkitRelativePath"
                :download="file.name"
                target="_blank"
                style="color: inherit; text-decoration: none"
              >
                {{ file.name }}
              </a>
            </span>
          </q-chip>
        </div>
        <div>
          <q-btn label="เพิ่มไฟล์" icon="attach_file" color="primary" @click="triggerFileInput" />

          <input
            ref="fileInputRef"
            type="file"
            multiple
            :accept="matchedItem?.questions?.[0]?.acceptFile ?? ''"
            style="display: none"
            @change="onFileChange"
          />
        </div>
      </div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import type { QTableColumn } from 'quasar';
import { type Assessment, type Option } from 'src/types/models';
import type { Response, ItemBlock } from 'src/types/models';
import { ResponsesService } from 'src/services/asm/responseService';

import { computed, reactive, ref, watch } from 'vue';
import { OptionService } from 'src/services/asm/optionService';
import apiService from 'src/services/apiService';

const props = defineProps<{
  id: number;
  draftId: number;
  category: string;
  item: Assessment;
  section: number;
  status: boolean;
  clear: boolean;
}>();

//answer
const selectedAnswer = ref<string>('');
const selectedAnswers = ref<string[]>([]);
const textAnswer = ref('');
const gridAnswers = reactive<{ [key: number]: number }>({});
const lastGridAnswer = ref<{ [key: number]: number }>();
const response = ref<Response>();
const userAnswer = ref<Response[]>();
const isEdit = ref(false);
const allResponse = ref<number[]>([]);
const fileInputRef = ref<HTMLInputElement | null>(null); // สำหรับกดเลือกไฟล์
const files = ref<File[]>([]); // สำหรับเก็บรายการไฟล์ที่เลือก
const isRequired = ref(true); // ตั้งค่าให้ true ถ้าจำเป็น
const isInvalid = ref(false);
const MAX_FILES = ref(1);
const MAX_SIZE_MB = ref(10);
const isPreview = ref(true);
const matchedItem = ref<ItemBlock | null>(null);
const hasInteracted = ref(false);

const onRadioChange = async () => {
  hasInteracted.value = true;
  isInvalid.value = false;
  await emitAnswer(); // หรือ emit อะไรก็ตาม
};

watch(
  () => props.draftId,
  async (newDraftId) => {
    matchedItem.value =
      props.item?.itemBlocks
        ?.slice() // เพื่อไม่แก้ของเดิม
        .sort((a, b) => a.sequence - b.sequence) // 🔢 เรียงตาม sequence
        .find((item) => item.id === props.id && item.section === props.section) || null;
    isRequired.value = matchedItem.value?.isRequired || false;
    const questions = matchedItem.value?.questions ?? [];
    if (props.category === 'UPLOAD') {
      // สมมติ questions[0]?.sizeLimit ได้เป็น bytes เช่น 5242880 (5MB)
      const sizeLimitBytes = questions[0]?.sizeLimit ?? 5 * 1024 * 1024;

      MAX_SIZE_MB.value = sizeLimitBytes / (1024 * 1024);
      MAX_SIZE_MB.value = questions[0]?.sizeLimit ?? 5;
    }

    if (questions.length === 0) return;

    if (props.status === true /* isPreview */) {
      // 🟢 preview mode → แค่ map คำถาม ไม่ต้องโหลดคำตอบ
      userAnswer.value = [];
      selectedAnswer.value = '';
      selectedAnswers.value = [];
      textAnswer.value = '';
      return;
    }

    if (!newDraftId) return;

    const isCheckbox = props.category === 'CHECKBOX';
    const answers: Response[] = [];

    for (const question of questions) {
      const res = await new ResponsesService().findAnswers(props.draftId, question.id);

      if (Array.isArray(res)) {
        answers.push(...res);

        // ✅ เก็บ id ทั้งหมดลง allResponse
        const ids = res.map((a) => a?.id).filter((id): id is number => typeof id === 'number');
        allResponse.value.push(...ids);

        if (isCheckbox) {
          selectedAnswers.value = res
            .map((a) => a.selectedOptionId)
            .filter((id): id is number => typeof id === 'number')
            .map((id) => String(id));
        } else if (props.category === 'GRID') {
          res.forEach((ans) => {
            if (ans.questionId && ans.selectedOptionId) {
              gridAnswers[ans.questionId] = ans.selectedOptionId;
            }
          });
        } else if (props.category === 'UPLOAD') {
          // ไม่ต้องทำอะไรเพิ่มเติม เพราะ res ถูก push ไปแล้วด้านบน
        }
      } else {
        // ถ้า res ไม่ใช่ array เช่นในกรณี category ทั่วไป
        const answer = await new ResponsesService().findAnswer(newDraftId, question.id);
        if (answer.data) {
          answers.push(answer.data);
          if (typeof answer.data.id === 'number') {
            allResponse.value.push(answer.data.id);
          }
        }
      }
    }
    userAnswer.value = answers;

    if (props.category === 'TEXTFIELD' && userAnswer.value) {
      textAnswer.value = userAnswer.value?.[0]?.selectedOption?.optionText ?? '';
    } else if (props.category === 'RADIO' && userAnswer.value) {
      selectedAnswer.value = String(userAnswer.value?.[0]?.selectedOptionId ?? '0');
    } else if (props.category === 'UPLOAD' && userAnswer.value) {
      const filesFromServer = await Promise.all(
        userAnswer.value
          .filter((answer) => typeof answer.selectedOption?.imagePath === 'string')
          .map(async (answer) => {
            const path = answer.selectedOption!.imagePath!;
            const fileResponse = await apiService.getPublicFile(path);
            const fileUrl = fileResponse.data; // เปลี่ยนเป็น fileResponse.data.url ถ้า backend ส่งแบบนั้น

            const response = await fetch(fileUrl);
            const blob = await response.blob();
            const filename = answer.selectedOption?.optionText || 'unknown';
            return new File([blob], filename, { type: blob.type });
          }),
      );

      files.value = filesFromServer;
    }
  },
  { immediate: true },
);

//validate
const validateAnswer = () => {
  let valid = true;

  if (!isRequired.value) return true;

  switch (props.category) {
    case 'RADIO':
      console.log('radio', selectedAnswer.value);
      valid = selectedAnswer.value !== '';
      console.log(valid);
      break;

    case 'CHECKBOX':
      valid = Array.isArray(selectedAnswers.value) && selectedAnswers.value.length > 0;
      break;

    case 'TEXTFIELD':
      console.log(textAnswer.value);
      console.log('valid: ', valid);
      valid = !!textAnswer.value?.trim();
      break;

    case 'GRID':
      valid = rows.value.length > 0 && rows.value.every((row) => gridAnswers[row.id] !== undefined);
      break;

    case 'UPLOAD':
      valid = Array.isArray(files.value) && files.value.length > 0;
      break;

    default:
      valid = true;
  }

  isInvalid.value = !valid;
  return valid;
};

defineExpose({ validateAnswer });

if (props.status === true) {
  isPreview.value = true;
} else {
  isPreview.value = false;
}
type AnswerValue = string | string[] | Record<string, number> | File[];

//checkbox
let previousSelected: string[] = [];
const onCheckboxChange = async (newVal: string[]) => {
  isInvalid.value = false;

  const added = newVal.filter((val) => !previousSelected.includes(val));
  const removed = previousSelected.filter((val) => !newVal.includes(val));

  try {
    const questionId = matchedItem.value?.questions?.[0]?.id;
    if (!questionId) return;
    const res = await new ResponsesService().findAnswer(props.draftId, questionId);
    if (res) {
      response.value = res.data;
    }

    if (response.value) {
      for (const optionId of removed) {
        const selectedOptionId = Number(optionId);
        if (isNaN(selectedOptionId)) continue;

        const removeAnswer = await new ResponsesService().findRemoveCheckBoxAnswer(
          props.draftId,
          questionId,
          selectedOptionId,
        );

        if (removeAnswer) {
          await new ResponsesService().remove(removeAnswer.id);
        }
      }
    }

    for (const optionId of added) {
      const selectedOptionId = Number(optionId);
      if (isNaN(selectedOptionId)) continue;

      const payload = {
        id: 0,
        submissionId: props.draftId,
        questionId,
        selectedOptionId,
      };
      await new ResponsesService().create(payload);
    }
  } catch (err) {
    console.log('❌ เพิ่มข้อมูลไม่สำเร็จ', err);
  }

  previousSelected = [...newVal];
};

const emit = defineEmits<{
  (event: 'update-answer', payload: { id: string | number; value: AnswerValue }): void;
}>();

const answerValue: AnswerValue =
  props.category === 'CHECKBOX'
    ? selectedAnswers.value
    : props.category === 'GRID'
      ? gridAnswers
      : props.category === 'RADIO'
        ? selectedAnswer.value
        : props.category === 'TEXTFIELD'
          ? textAnswer.value
          : props.category === 'UPLOAD'
            ? files.value // 🔁 เปลี่ยนจาก fileInputRef.value เป็น files.value
            : '';

emit('update-answer', {
  id: props.id,
  value: answerValue,
});

const emitAnswer = async () => {
  let value: AnswerValue = '';

  if (props.category === 'GRID') {
    const entries = Object.entries(gridAnswers);
    const lastEntry = entries.at(-1);
    if (lastEntry) {
      const [lastKey, lastValue] = lastEntry;
      lastGridAnswer.value = { [parseInt(lastKey, 10)]: lastValue };
      value = lastGridAnswer.value;
    } else {
      lastGridAnswer.value = {};
      value = {};
    }
  } else if (props.category === 'CHECKBOX') {
    value = selectedAnswers.value;
  } else if (props.category === 'TEXTFIELD') {
    isInvalid.value = false;
    value = textAnswer.value;
  } else if (props.category === 'RADIO') {
    value = selectedAnswer.value;
  } else if (props.category === 'UPLOAD') {
    value = files.value ?? [];
  }

  emit('update-answer', {
    id: props.id,
    value,
  });

  try {
    if (props.category === 'TEXTFIELD') {
      const questionId = matchedItem.value?.questions?.[0]?.id;
      if (questionId) {
        const res = await new ResponsesService().findAnswer(props.draftId, questionId);
        if (res) {
          response.value = res.data;
        }
        if (response.value?.id && response.value.selectedOptionId) {
          const option = await new OptionService().getOptionById(response.value.selectedOptionId);
          const upDateAnswerText: Option = {
            id: option.id,
            itemBlockId: option.itemBlockId,
            optionText: textAnswer.value,
            value: option.value,
            sequence: option.sequence,
          };
          await new OptionService().updateOption(response.value.selectedOptionId, upDateAnswerText);
        } else {
          const answerText: Option = {
            id: 0,
            itemBlockId: props.id,
            optionText: textAnswer.value,
            value: 0,
            sequence: 0,
          };
          const createdOption = await new OptionService().createOption(answerText);

          const payload: Response = {
            id: 0,
            submissionId: props.draftId,
            questionId: questionId,
            selectedOptionId: createdOption.id,
          };
          await new ResponsesService().create(payload);
        }
      }
    } else if (props.category === 'RADIO') {
      const questionId = matchedItem.value?.questions?.[0]?.id;

      if (questionId) {
        const res = await new ResponsesService().findAnswer(props.draftId, questionId);
        if (res) {
          response.value = res.data;
        }
        if (response.value?.id && response.value.questionId) {
          const updatePayload: Response = {
            id: response.value.id,
            submissionId: response.value.submissionId,
            questionId: response.value.questionId,
            selectedOptionId: props.category === 'RADIO' ? parseInt(value as string, 10) : 0,
          };
          await new ResponsesService().update(updatePayload.id, updatePayload);
        } else {
          const payload: Response = {
            id: 0,
            submissionId: props.draftId,
            questionId: questionId,
            selectedOptionId: props.category === 'RADIO' ? parseInt(value as string, 10) : 0,
          };

          await new ResponsesService().create(payload);
        }
      }
    } else if (props.category === 'GRID' && isEdit.value === true) {
      isInvalid.value = false;
      for (const [questionIdStr, selectedOptionId] of Object.entries(gridAnswers)) {
        const questionId = Number(questionIdStr);
        const res = await new ResponsesService().findAnswer(props.draftId, questionId);
        if (!res.data || Object.keys(res.data).length === 0) {
          // สร้างใหม่ ถ้า res.data เป็น undefined/null หรือเป็น object ว่าง
          const payload: Response = {
            id: 0,
            submissionId: props.draftId,
            questionId,
            selectedOptionId,
          };
          await new ResponsesService().create(payload);
        } else {
          const updatePayload: Response = {
            id: res.data.id,
            submissionId: props.draftId,
            questionId,
            selectedOptionId,
          };
          if (updatePayload.id) {
            await new ResponsesService().update(updatePayload.id, updatePayload);
          }
        }
      }
    }
  } catch (error) {
    console.error('Failed to send response to backend:', error);
  }
};

watch(
  gridAnswers,
  async () => {
    await emitAnswer();
  },
  { deep: true },
);

watch(
  matchedItem,
  (newItem) => {
    if (newItem) {
      newItem.section = props.section;
    }
  },
  { immediate: true },
);

interface Row {
  id: number;
  question: string;
  [key: `choice_${number}`]: string;
}

const columns = computed<QTableColumn[]>(() => {
  const base: QTableColumn[] = [
    {
      name: 'question',
      label: 'คำถาม',
      field: 'question',
      align: 'left',
    },
  ];

  const choices: QTableColumn[] =
    matchedItem.value?.options?.map((opt) => ({
      name: `choice_${opt.id}`, // ใช้ option.id แทน index
      label: opt.optionText,
      field: `choice_${opt.id}`,
      align: 'center',
    })) ?? [];

  return base.concat(choices);
});

const rows = computed<Row[]>(() => {
  if (!matchedItem.value?.questions) return [];

  return matchedItem.value.questions
    .filter((q) => !q.isHeader) // กรองเอาเฉพาะคำถามที่ไม่ใช่ header
    .map((q) => {
      const row: Row = {
        id: q.id,
        question: q.questionText,
      };

      matchedItem.value?.options?.forEach((_, i) => {
        row[`choice_${i}`] = '';
      });
      matchedItem.value?.options?.forEach((_, i) => {
        row[`choice_${i}`] = '';
      });

      return row;
    });
});

type FileWithUrl = {
  file: File;
  url: string;
};
const filesWithUrl = ref<FileWithUrl[]>([]);

function triggerFileInput() {
  fileInputRef.value?.click();
}

function handleFiles(selectedFiles: FileList) {
  const newFiles = Array.from(selectedFiles).map((file) => ({
    file,
    url: URL.createObjectURL(file),
  }));
  filesWithUrl.value.push(...newFiles);
}

async function onFileChange(event: Event) {
  isInvalid.value = false;
  const target = event.target as HTMLInputElement;
  const newFiles = target.files ? Array.from(target.files) : [];

  if (newFiles.length === 0) return;

  // เช็คจำนวนไฟล์รวม
  if (files.value.length + newFiles.length > MAX_FILES.value) {
    alert(`เลือกไฟล์ได้สูงสุด ${MAX_FILES.value} ไฟล์เท่านั้น`);
    target.value = '';
    return;
  }

  // เช็คขนาดไฟล์
  const maxSizeInBytes = MAX_SIZE_MB.value * 1024 * 1024;
  const tooLarge = newFiles.find((f) => f.size > MAX_SIZE_MB.value);

  if (tooLarge) {
    alert(`ไฟล์ "${tooLarge.name}" มีขนาดเกิน ${maxSizeInBytes} MB`);
    target.value = '';
    return;
  }

  // เช็คชนิดไฟล์ accept จาก matchedItem
  if (matchedItem.value) {
    const acceptTypes = (matchedItem?.value.questions?.[0]?.acceptFile ?? '')
      .split(',')
      .map((s: string) => s.trim())
      .filter(Boolean);

    if (acceptTypes.length > 0) {
      const invalid = newFiles.find((file) => {
        return !acceptTypes.some((type) => {
          if (type.startsWith('.')) {
            return file.name.toLowerCase().endsWith(type.toLowerCase());
          }
          if (type === 'image/*') {
            return file.type.startsWith('image/');
          }
          return file.type === type;
        });
      });

      if (invalid) {
        alert(`ไฟล์ "${invalid.name}" ไม่ตรงกับชนิดไฟล์ที่อนุญาต`);
        target.value = '';
        return;
      }
    }
  }

  // ผ่านทุกอย่างแล้ว เพิ่มไฟล์เก็บไว้
  files.value.push(...newFiles);

  if (target.files) {
    handleFiles(target.files);
  }

  target.value = ''; // reset เพื่อให้เลือกไฟล์เดิมได้

  // อัปโหลดไฟล์ใหม่เฉพาะกรณี category เป็น UPLOAD
  if (props.category === 'UPLOAD') {
    const questionId = matchedItem.value?.questions?.[0]?.id;
    if (!questionId) return;

    try {
      const res = await new ResponsesService().findAnswer(props.draftId, questionId);
      if (res) {
        response.value = res.data;
      }

      for (const file of newFiles) {
        const newOption: Option = {
          id: 0,
          itemBlockId: props.id,
          optionText: file.name,
          imagePath: file.name,
          value: 0,
          sequence: 0,
        };

        const createdOption = await new OptionService().uploadOptionWithFile(newOption, file);

        const payload: Response = {
          id: 0,
          submissionId: props.draftId,
          questionId: questionId,
          selectedOptionId: createdOption.id,
        };

        await new ResponsesService().create(payload);
      }
    } catch (error) {
      console.error('Error uploading files:', error);
    }
  }
}

// ลบไฟล์ออก
async function removeFile(index: number) {
  const name = files.value[index]?.name;
  if (name) {
    const imagePath = await new OptionService().findImagePath(name);
    await apiService.deleteFile(imagePath);
    await new OptionService().removeFileName(name);
    files.value.splice(index, 1);
  }
}
watch(
  () => props.clear,
  async (isClear) => {
    if (isClear) {
      if (props.category === 'TEXTFIELD') {
        textAnswer.value = '';
      } else if (props.category === 'RADIO') {
        selectedAnswer.value = '';
      } else if (props.category === 'CHECKBOX') {
        selectedAnswers.value = [];
      } else if (props.category === 'GRID') {
        for (const key in gridAnswers) {
          delete gridAnswers[key];
        }
      }
    }
    if (allResponse.value.length > 0) {
      for (const responseId of allResponse.value) {
        await new ResponsesService().clearAnswer(responseId);
      }
    }
  },
  { immediate: true },
);

// blur → save (เฉพาะ shortAnswer เพราะ radio/checkbox ไม่มี blur)
</script>

<style scoped lang="scss">
.title {
  font-size: 20px;
}

.required {
  color: red;
  font-size: 20px;
  margin-left: 4px;
}
.group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.file-upload {
  background-color: white;
  color: $primary;
  border: 1px solid;
  border-color: $surface;
}

.font-size {
  font-size: 18px;
}
.grid-choice {
  width: 100%;
  display: flex;
  justify-content: center;
}

.grid-table {
  width: 100%;
  max-width: 800px;
  table-layout: fixed;

  .label-column {
    width: 50%;
    max-width: 50%;
    word-break: break-all;
    white-space: normal;
    overflow-wrap: break-word;
  }

  .option-column {
    width: calc(50% / 5);
    padding: 8px;
    text-align: center;

    &:first-child {
      border-left: none;
    }
  }
}
.custom-file {
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 10px 16px;
  font-weight: bold;
  display: inline-block;
  transition: background-color 0.3s;
  max-width: 150px;
  max-height: 60px;
  height: 100%;
  width: 100%;
}

.custom-file:hover {
  background-color: #fffbed;
}
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
  display: inline-block;
}
.border-error {
  border: 1px solid red;
  border-radius: 10px;
}
</style>
