<template>
  <q-page>
    <draggable
      v-model="draggableBlocks"
      :component-data="{
        tag: 'div',
        type: 'transition-group',
        name: !blockCreatorStore.isDragging ? 'flip-list' : null,
      }"
      item-key="id"
      handle=".three-dot-menu"
      :animation="200"
      ghost-class="ghost"
      chosen-class="chosen"
      drag-class="drag"
      @start="onDragStart"
      @end="onDragEnd"
      @change="onDragChange"
    >
      <template #item="{ element: block, index }">
        <div
          :key="`${block.id}-${blockCreatorStore.totalSections}-${blockCreatorStore.forceUpdateTrigger}`"
          :ref="(el) => (blockCreatorUIStore.blockRefs[block.id] = el)"
          class="row justify-center draggable-item"
          :class="{ 'is-dragging': blockCreatorStore.isDragging }"
        >
          <div class="col-auto">
            <div
              v-if="blockCreatorStore.isSectionBlock(index) && blockCreatorStore.totalSections > 1"
              class="col-12 section-container"
              :key="`section-${blockCreatorStore.getSectionNumber(index)}-${blockCreatorStore.totalSections}-${blockCreatorStore.forceUpdateTrigger}-${block.id}`"
            >
              <div class="section-tab">
                ส่วนที่ {{ blockCreatorStore.getSectionNumber(index) }} จาก
                {{ blockCreatorStore.totalSections }}
              </div>
            </div>
            <div class="block-container">
              <div class="block-content full-width">
                <template v-if="block.type === 'HEADER'">
                  <HeaderBlock
                    :itemBlock="block"
                    :index="index"
                    :type="props.type"
                    class="evaluate-item"
                    :class="{
                      'no-top-left-radius':
                        blockCreatorStore.isSectionBlock(index) &&
                        blockCreatorStore.totalSections > 1,
                    }"
                    @focus-fab="blockCreatorUIStore.handleFocusFab(block.id)"
                    @duplicate="handleDuplicateHeaderBlock"
                    @delete="
                      () => {
                        const currentIndex = blockCreatorStore.blocks.findIndex(
                          (b) => b.id === block.id,
                        );
                        onClickDeleteBlock(block, currentIndex);
                      }
                    "
                  />
                </template>

                <template v-else-if="block.type === 'IMAGE'">
                  <ImageBlock
                    :item-block="block"
                    class="evaluate-item"
                    @focus-fab="blockCreatorUIStore.handleFocusFab(block.id)"
                    @duplicate="handleDuplicateBlock"
                    @delete="
                      () => {
                        const currentIndex = blockCreatorStore.blocks.findIndex(
                          (b) => b.id === block.id,
                        );
                        onClickDeleteBlock(block, currentIndex);
                      }
                    "
                    @update:image="handleImageUpdate"
                  />
                </template>

                <template v-else>
                  <ItemBlockProvider :block-id="block.id" :item-block="block">
                    <!-- <ItemBlockComponent
                      :item-block="block"
                      :type="props.type"
                      class="evaluate-item"
                      @focus-fab="blockCreatorUIStore.handleFocusFab(block.id)"
                      @duplicate="handleDuplicateBlock"
                      @delete="() => onClickDeleteBlock(block, index)"
                      @update:question="blockCreatorUIStore.handleQuestionUpdate"
                      @update:option="blockCreatorUIStore.handleOptionUpdate"
                      @update:is-required="blockCreatorUIStore.handleIsRequiredUpdate"
                    /> -->
                    <ItemBlockComponent
                      :item-block="block"
                      :type="props.type"
                      :question-number="getQuestionNumber(index)"
                      class="evaluate-item"
                      @focus-fab="blockCreatorUIStore.handleFocusFab(block.id)"
                      @duplicate="handleDuplicateBlock"
                      @delete="
                        () => {
                          const currentIndex = blockCreatorStore.blocks.findIndex(
                            (b) => b.id === block.id,
                          );
                          onClickDeleteBlock(block, currentIndex);
                        }
                      "
                      @update:question="handleUpdateQuestion"
                      @update:option="handleUpdateOption"
                      @update:is-required="blockCreatorUIStore.handleIsRequiredUpdate"
                      @refresh-assessment="
                        ({ focusBlockId }) => refreshAssessmentData(focusBlockId)
                      "
                    />
                  </ItemBlockProvider>
                </template>
              </div>
            </div>
          </div>

          <div class="col-auto fixed-fab-col">
            <FloatActionBtnForBlock
              v-show="
                blockCreatorStore.selectedBlockId === `block-${block.id}` &&
                !blockCreatorStore.isDragging
              "
              :disabled="blockCreatorStore.isCreatingBlock"
              :type="props.type"
              @add="
                () => {
                  // Find the current index of this block (it may have shifted after previous insertions)
                  const currentIndex = blockCreatorStore.blocks.findIndex((b) => b.id === block.id);
                  console.log('before addBlockAfter', props.assessmentId);

                  blockCreatorStore.handleAddBlockAfter(
                    currentIndex,
                    props.type,
                    props.assessmentId,
                  );
                }
              "
              @add-text="
                () => {
                  // Find the current index of this block (it may have shifted after previous insertions)
                  const currentIndex = blockCreatorStore.blocks.findIndex((b) => b.id === block.id);
                  blockCreatorStore.handleAddHeaderAfter(
                    currentIndex,
                    props.type,
                    props.assessmentId,
                    false,
                  );
                }
              "
              @add-section="handleAddSection"
              @add-image="
                (payload) => {
                  // Find the current index of this block (it may have shifted after previous insertions)
                  const currentIndex = blockCreatorStore.blocks.findIndex((b) => b.id === block.id);
                  handleAddImageBlock(currentIndex, payload);
                }
              "
              @image-uploaded="handleImageUploaded"
            />
          </div>
        </div>
      </template>
    </draggable>
    <div ref="sentinel" class="q-mt-md" />
  </q-page>
</template>

<script setup lang="ts">
import { watch, nextTick, onMounted, onUnmounted, computed, ref } from 'vue';
import { useBlockCreatorStore } from 'src/stores/block_creator';
import { useBlockCreatorUIStore } from 'src/stores/block_creator_ui';
import type { ItemBlock, Option } from 'src/types/models';
import HeaderBlock from './HeaderBlock.vue';
import ItemBlockComponent from './ItemBlockComponent.vue';
import FloatActionBtnForBlock from './FloatActionBtnForBlock.vue';
import ItemBlockProvider from './ItemBlockProvider.vue';
import ImageBlock from './ImageBlock.vue';
// import { defaultBlocks } from 'src/data/defaultBlocks';
import draggable from 'vuedraggable';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useGlobalStore } from 'src/stores/global';
import { duplicateBlockAtomic, getCurrentSection } from 'src/utils/block_helper';

const props = defineProps<{
  blocks?: ItemBlock[];
  type: 'quiz' | 'evaluate';
  assessmentId?: number | null;
}>();

defineOptions({
  name: 'block-creator',
});

// Store instances
const blockCreatorStore = useBlockCreatorStore();
const blockCreatorUIStore = useBlockCreatorUIStore();
const globalStore = useGlobalStore();

// State variables
const isDragging = ref(false);
const blockCreationInProgress = ref(false);
const targetBlockId = ref<number | null>(null);
const fabPositionLock = ref(false);
const isCreatingBlock = ref(false);
let scrollTimeout: NodeJS.Timeout | null = null;

// Memoized assessment service - only recreate when type changes
const assessmentService = computed(() => new AssessmentService(props.type));

// const refreshAssessmentData = async (focusBlockId?: number | null) => {
//   try {
//     const currentSelectedBlockId = blockCreatorStore.selectedBlockId;
//     const currentBlockId = currentSelectedBlockId
//       ? Number(currentSelectedBlockId.split('-')[1])
//       : null;
//     const blockIdToFocus = focusBlockId || currentBlockId;

//     // 👉 ดึงข้อมูลใหม่จาก backend
//     const { assessment, pagedItemBlocks } = await assessmentService.value.fetchOne(
//       props.assessmentId ?? 0,
//     );

//     // ✅ เซต itemBlocks ลง currentAssessment
//     blockCreatorStore.currentAssessment = {
//       ...assessment,
//       itemBlocks: pagedItemBlocks,
//     };

//     // ✅ เซตลง store และ refresh
//     blockCreatorStore.initializeBlocks(pagedItemBlocks);
//     await blockCreatorStore.forceRefreshBlocks();

//     // ✅ scroll ไปยัง block ที่ต้องการ focus
//     if (blockIdToFocus) {
//       const blockStillExists = blockCreatorStore.blocks.some(
//         (block) => block.id === blockIdToFocus,
//       );

//       if (blockStillExists) {
//         blockCreatorStore.selectedBlockId = `block-${blockIdToFocus}`;
//         await nextTick();
//         scrollToTarget();
//       }
//     }

//     return true;
//   } catch (error) {
//     console.error('An error occurred in refreshAssessmentData:', error);
//     return false;
//   }
// };

async function handleUpdateOption(payload: {
  action: 'created' | 'updated' | 'refresh-option';
  itemBlockId: number;
  option?: Option;
  optionId?: number;
  updateData?: { index: number; option: Option };
}) {
  console.log('🔄 [OPTION-UPDATE] Handling option update:', payload);

  if (payload.action === 'refresh-option') {
    try {
      // ดึง block เดี่ยวจาก backend
      const { ItemBlockService } = await import('src/services/asm/itemBlockService');
      const itemBlockService = new ItemBlockService();
      const updatedBlock = await itemBlockService.getOne(payload.itemBlockId);

      if (!updatedBlock) return;

      // หา index ของ block ใน store
      const blockIndex = blockCreatorStore.blocks.findIndex(
        (block) => block.id === payload.itemBlockId,
      );
      if (blockIndex !== -1) {
        blockCreatorStore.updateBlock(updatedBlock, blockIndex);
      }
    } catch (error) {
      console.error('Failed to update option for block:', payload.itemBlockId, error);
    }
  } else if (payload.action === 'created' || payload.action === 'updated') {
    // ✅ DELEGATE TO UI STORE for proper state management
    // This ensures that both local blocks and currentAssessment are updated
    const uiStorePayload: {
      action: 'created' | 'updated';
      itemBlockId: number;
      option?: Option;
      optionId?: number;
      updateData?: { index: number; option: Option };
    } = {
      action: payload.action,
      itemBlockId: payload.itemBlockId,
      ...(payload.option && { option: payload.option }),
      ...(payload.optionId && { optionId: payload.optionId }),
      ...(payload.updateData && { updateData: payload.updateData }),
    };

    blockCreatorUIStore.handleOptionUpdate(uiStorePayload);

    console.log('✅ [OPTION-UPDATE] State updated via UI store for action:', payload.action);
  }
}

const refreshAssessmentData = async (focusBlockId?: number | null) => {
  try {
    // ✅ ENHANCED ASSESSMENT ID RETRIEVAL with multiple fallbacks
    let assessmentId = props.assessmentId ?? blockCreatorStore.getAssessmentId();

    // ✅ FALLBACK 1: Try to get from route parameters
    if (!assessmentId || assessmentId <= 0) {
      const { useRoute } = await import('vue-router');
      const route = useRoute();
      const routeId = Number(route.params.id);
      if (routeId && routeId > 0) {
        assessmentId = routeId;
        console.log('🔄 [REFRESH] Using assessment ID from route:', assessmentId);
      }
    }

    // ✅ FALLBACK 2: Try to get from current assessment in store
    if (!assessmentId || assessmentId <= 0) {
      const currentAssessment = blockCreatorStore.currentAssessment;
      if (currentAssessment?.id) {
        assessmentId = currentAssessment.id;
        console.log('🔄 [REFRESH] Using assessment ID from current assessment:', assessmentId);
      }
    }

    // ✅ FALLBACK 3: Try to get from any block's assessmentId
    if (!assessmentId || assessmentId <= 0) {
      const firstBlock = blockCreatorStore.blocks?.[0];
      if (firstBlock?.assessmentId) {
        assessmentId = firstBlock.assessmentId;
        console.log('🔄 [REFRESH] Using assessment ID from first block:', assessmentId);
      }
    }

    // ✅ FINAL VALIDATION
    if (!assessmentId || assessmentId <= 0) {
      console.error('❌ [REFRESH] Invalid assessment ID after all fallbacks:', {
        propsAssessmentId: props.assessmentId,
        storeAssessmentId: blockCreatorStore.getAssessmentId(),
        currentAssessmentId: blockCreatorStore.currentAssessment?.id,
        firstBlockAssessmentId: blockCreatorStore.blocks?.[0]?.assessmentId,
        blocksCount: blockCreatorStore.blocks?.length || 0,
      });
      throw new Error('Assessment ID is required for refresh operation');
    }

    console.log('🔄 [REFRESH] Starting assessment refresh:', {
      assessmentId,
      focusBlockId,
      type: props.type,
    });

    // ดึง blockId ที่ต้องการ focus (ถ้ามี)
    const currentSelectedBlockId = blockCreatorStore.selectedBlockId;
    const currentBlockId = currentSelectedBlockId
      ? Number(currentSelectedBlockId.split('-')[1])
      : null;
    const blockIdToFocus = focusBlockId || currentBlockId;

    // โหลด "หน้าแรก" เสมอ (หรือจะเปลี่ยน page ตามต้องการก็ได้)
    const page = 1;
    const limit = 10;

    // ✅ USE VALIDATED ASSESSMENT ID
    const { assessment, pagedItemBlocks } = await assessmentService.value.fetchOne(assessmentId, {
      page,
      limit,
    });

    console.log('✅ [REFRESH] Assessment data fetched successfully:', {
      assessmentId: assessment.id,
      blocksCount: pagedItemBlocks.length,
      focusBlockId: blockIdToFocus,
    });

    // เซตข้อมูลลง store
    blockCreatorStore.currentAssessment = {
      ...assessment,
      itemBlocks: pagedItemBlocks,
    };

    blockCreatorStore.resetStore();
    blockCreatorStore.initializeBlocks(pagedItemBlocks);
    await blockCreatorStore.forceRefreshBlocks();

    blockCreatorStore.itemBlockPage = page;
    blockCreatorStore.hasMoreBlocks = pagedItemBlocks.length === limit;

    // scroll ไป block ที่ต้องการ (ถ้าอยู่ในหน้านี้)
    if (blockIdToFocus) {
      const blockStillExists = blockCreatorStore.blocks.some(
        (block) => block.id === blockIdToFocus,
      );
      if (blockStillExists) {
        blockCreatorStore.selectedBlockId = `block-${blockIdToFocus}`;
        await nextTick();
        scrollToTarget();
      }
    }

    return true;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : undefined;

    console.error('❌ [REFRESH] refreshAssessmentData failed:', {
      error: errorMessage,
      assessmentId: props.assessmentId,
      focusBlockId,
      stack: errorStack,
    });
    return false;
  }
};
async function loadMoreBlocks(assessmentId: number, type: 'quiz' | 'evaluate') {
  if (!blockCreatorStore.hasMoreBlocks) return;

  const nextPage = blockCreatorStore.itemBlockPage + 1;
  const limit = 10;

  const service = new AssessmentService(type);
  const { pagedItemBlocks } = await service.fetchOne(assessmentId, { page: nextPage, limit });

  if (pagedItemBlocks.length < limit) {
    blockCreatorStore.hasMoreBlocks = false;
  }

  blockCreatorStore.itemBlockPage = nextPage;

  // 👇 ต้องมี method `addBlocks()` ใน blockCreatorStore ด้วย
  blockCreatorStore.addBlocks(pagedItemBlocks);

  if (props.type === 'evaluate' && blockCreatorStore.currentAssessment?.itemBlocks) {
    blockCreatorStore.currentAssessment.itemBlocks = [
      ...blockCreatorStore.currentAssessment.itemBlocks,
      ...pagedItemBlocks,
    ];
  }
}

// Draggable blocks computed property
const draggableBlocks = computed({
  get: () => blockCreatorStore.blocks.slice().sort((a, b) => a.sequence - b.sequence),
  set: (newBlocks: ItemBlock[]) => {
    blockCreatorStore.updateBlocksOrder(newBlocks);
  },
});

// เพิ่มใน <script setup>
const observer = ref<IntersectionObserver | null>(null);
const sentinel = ref<HTMLElement | null>(null); // element ด้านล่างสุด

// onMounted(() => {
//   observer.value = new IntersectionObserver(
//     (entries) => {
//       const entry = entries[0];
//       if (entry && entry.isIntersecting && blockCreatorStore.hasMoreBlocks) {
//         (async () => {
//           await loadMoreBlocks(props.assessmentId!, props.type);
//         })().catch((err) => {
//           console.error('loadMoreBlocks error:', err);
//         });
//       }
//     },
//     {
//       root: null,
//       threshold: 1.0,
//     },
//   );

//   if (sentinel.value) {
//     observer.value.observe(sentinel.value);
//   }
// });

onMounted(async () => {
  observer.value = new IntersectionObserver(
    (entries) => {
      const entry = entries[0];
      if (entry && entry.isIntersecting && blockCreatorStore.hasMoreBlocks) {
        console.log('📍 Sentinel intersected, loading more blocks...');
        (async () => {
          await loadMoreBlocks(props.assessmentId!, props.type);
        })().catch((err) => {
          console.error('loadMoreBlocks error:', err);
        });
      }
    },
    {
      root: null,
      threshold: 1.0,
    },
  );

  // ✅ รอให้ sentinel ถูก mount แล้ว
  await nextTick();

  if (sentinel.value) {
    console.log('✅ Observing sentinel', sentinel.value); // Debug
    observer.value.observe(sentinel.value);
  } else {
    console.warn('⚠️ Sentinel element not found');
  }
});

onUnmounted(() => {
  if (observer.value && sentinel.value) {
    observer.value.unobserve(sentinel.value);
  }
});

// Function to initialize blocks based on current data
// const initializeBlocksFromData = async () => {
//   let blocksToUse: ItemBlock[] = [];

//   if (props.type === 'evaluate') {
//     // For evaluate type, get blocks from the block creator store
//     const assessmentBlocks = blockCreatorStore.currentAssessment?.itemBlocks;
//     if (assessmentBlocks && assessmentBlocks.length > 0) {
//       blocksToUse = assessmentBlocks;
//     } else if (props.blocks && props.blocks.length > 0) {
//       blocksToUse = props.blocks;
//     } else {
//       // Only use default blocks as last resort for evaluate type
//       blocksToUse = defaultBlocks;
//     }
//   } else {
//     // For quiz type, use props or default blocks
//     blocksToUse = props.blocks && props.blocks.length > 0 ? props.blocks : defaultBlocks;
//   }

//   blockCreatorStore.initializeBlocks(blocksToUse);

//   // Initialize with first block selected ONLY if FAB is not locked (e.g., during image upload)
//   if (blockCreatorStore.blocks.length > 0 && !blockCreatorStore.fabPositionLock) {
//     blockCreatorStore.selectedBlockId = `block-${blockCreatorStore.blocks[0]!.id}`;
//     await nextTick();
//     blockCreatorStore.scrollToTarget();
//   }
// };

// const initializeBlocksFromData = async () => {
//   let blocksToUse: ItemBlock[] = [];

//   if (props.type === 'evaluate') {
//     // ✅ ดึงจาก currentAssessment.itemBlocks แทน blocks ใน store
//     const assessmentBlocks = blockCreatorStore.currentAssessment?.itemBlocks;
//     if (assessmentBlocks && assessmentBlocks.length > 0) {
//       blocksToUse = assessmentBlocks;
//     } else if (props.blocks?.length) {
//       blocksToUse = props.blocks;
//     }
//   } else {
//     blocksToUse = props.blocks?.length ? props.blocks : defaultBlocks;
//   }

//   // ✅ เซตลง store
//   blockCreatorStore.initializeBlocks(blocksToUse);

//   if (blocksToUse.length > 0 && !blockCreatorStore.fabPositionLock) {
//     blockCreatorStore.selectedBlockId = `block-${blocksToUse[0]!.id}`;
//     await nextTick();
//     blockCreatorStore.scrollToTarget();
//   }
// };

type UpdateQuestionPayload = {
  action: 'refresh-block';
  itemBlockId: number;
};

const handleUpdateQuestion = async (payload: UpdateQuestionPayload) => {
  if (payload.action === 'refresh-block') {
    try {
      // Use the centralized refreshAssessmentData function to update data
      await refreshAssessmentData(payload.itemBlockId);
    } catch (error) {
      console.error('An error occurred:', error);
    }
  }
};

onMounted(async () => {
  // await initializeBlocksFromData();
  await refreshAssessmentData();
});

// Cleanup all timeouts and intervals on unmount
onUnmounted(() => {
  if (scrollTimeout) {
    clearTimeout(scrollTimeout);
    scrollTimeout = null;
  }
  // Clear block refs cache
  blockCreatorUIStore.blockRefsCache.clear();
});

// Watch for changes in the block creator store's current assessment
// This ensures that when data is fetched from backend, the blocks are re-initialized
// watch(
//   () => blockCreatorStore.currentAssessment,
//   async (newAssessment) => {
//     if (props.type === 'evaluate' && newAssessment?.itemBlocks) {
//       await initializeBlocksFromData();
//     }
//   },
//   { deep: true, immediate: false },
// );

// Watch for changes in globalIsRequired to update all itemBlocks reactively
watch(
  () => blockCreatorStore.currentAssessment?.globalIsRequired,
  (newGlobalIsRequired) => {
    if (props.type === 'evaluate' && newGlobalIsRequired !== undefined) {
      blockCreatorStore.blocks.forEach((block, index) => {
        if (block.type !== 'HEADER' && block.type !== 'IMAGE') {
          const updatedBlock = {
            ...block,
            isRequired: Boolean(newGlobalIsRequired),
          };
          blockCreatorStore.updateBlock(updatedBlock, index);
        }
      });
    }
  },
  { immediate: false },
);

//log output
watch(
  () => draggableBlocks.value,
  (blocks) => {
    console.log('📦 draggableBlocks changed:', blocks.length);
    console.table(
      blocks.map((b, i) => ({
        index: i + 1,
        id: b.id,
        type: b.type,
        section: b.section,
        sequence: b.sequence,
        questionCount: b.questions?.length || 0,
      })),
    );
  },
  { immediate: true, deep: true },
);

// TODO: Move to store - temporary placeholder
const handleAddSection = async () => {
  // Call the store method to handle section addition
  await blockCreatorStore.handleAddSection(props.type, props.assessmentId);
};

// Handle HeaderBlock duplication with backend persistence
const handleDuplicateHeaderBlock = async () => {
  // Get the currently selected block
  const selectedBlockIdValue = blockCreatorStore.selectedBlockId;
  if (!selectedBlockIdValue) {
    return;
  }

  // Extract block ID from selectedBlockId (format: "block-123")
  const blockId = Number(selectedBlockIdValue.split('-')[1]);
  if (!blockId || isNaN(blockId)) {
    return;
  }

  // Call the store method to handle header block duplication
  await blockCreatorStore.handleDuplicateHeaderBlock(blockId, props.type, props.assessmentId);
};

// Handle ItemBlock duplication with ATOMIC backend operation
// ATOMIC SOLUTION: Uses single backend API call to eliminate race condition completely
const handleDuplicateBlock = async () => {
  // 1. Get the currently selected block
  const selectedBlockIdValue = blockCreatorStore.selectedBlockId;
  if (!selectedBlockIdValue) {
    return;
  }

  // 2. Extract block ID from selectedBlockId (format: "block-123")
  const blockId = Number(selectedBlockIdValue.split('-')[1]);
  if (!blockId || isNaN(blockId)) {
    return;
  }

  // 3. Find the source block in the store
  const sourceBlockIndex = blockCreatorStore.blocks.findIndex((block) => block.id === blockId);
  if (sourceBlockIndex === -1 || !blockCreatorStore.blocks[sourceBlockIndex]) {
    return;
  }
  const sourceBlock = blockCreatorStore.blocks[sourceBlockIndex];

  // 4. Prevent duplication during other operations
  if (isCreatingBlock.value) {
    return;
  }

  try {
    // 5. Set up state for duplication process
    isCreatingBlock.value = true;
    blockCreationInProgress.value = true;
    globalStore.startSaveOperation('Duplicating block (atomic operation)...');

    // 6. Get assessment ID and current section
    const assessmentId = props.assessmentId || blockCreatorStore.getAssessmentId();
    if (!assessmentId) {
      globalStore.completeSaveOperation(false, 'Assessment ID not found');
      return;
    }
    const currentSection = getCurrentSection(blockCreatorStore.blocks, sourceBlockIndex);

    // 7. ATOMIC DUPLICATION: Single backend call creates complete duplicate
    const duplicatedBlock = await duplicateBlockAtomic(
      blockId,
      {
        assessmentId,
        sequence: sourceBlock.sequence + 1, // Insert after source block
        section: currentSection,
      },
      assessmentService.value,
    );

    if (!duplicatedBlock) {
      globalStore.completeSaveOperation(false, 'Failed to duplicate block');
      return;
    }

    // 8. Update local state with the complete duplicated block
    const insertIndex = sourceBlockIndex + 1;
    // Use deep clone to avoid reference issues
    const clonedBlock = structuredClone
      ? structuredClone(duplicatedBlock)
      : JSON.parse(JSON.stringify(duplicatedBlock));
    blockCreatorStore.addBlock(clonedBlock, insertIndex);

    // 9. Update sequence numbers for all blocks
    blockCreatorStore.blocks.forEach((block, idx) => {
      if (block.sequence !== idx + 1) block.sequence = idx + 1;
    });

    // 10. Update store for evaluate type with deep cloning
    if (props.type === 'evaluate' && blockCreatorStore.currentAssessment) {
      const currentBlocks = blockCreatorStore.currentAssessment.itemBlocks || [];
      const clonedUpdatedBlock = structuredClone
        ? structuredClone(duplicatedBlock)
        : JSON.parse(JSON.stringify(duplicatedBlock));
      const newAssessmentBlocks = [
        ...currentBlocks.slice(0, insertIndex),
        clonedUpdatedBlock,
        ...currentBlocks.slice(insertIndex),
      ];

      newAssessmentBlocks.forEach((block, idx) => (block.sequence = idx + 1));
      blockCreatorStore.currentAssessment.itemBlocks = newAssessmentBlocks;
    }

    // 11. Set UI focus to duplicated block
    targetBlockId.value = duplicatedBlock.id;
    fabPositionLock.value = true;
    await setFabAndScroll(duplicatedBlock.id);
    blockCreatorStore.selectedBlockId = `block-${duplicatedBlock.id}`;

    // 12. Complete operation
    const hasContent =
      (duplicatedBlock.type === 'IMAGE' && duplicatedBlock.imageBody) ||
      (duplicatedBlock.options && duplicatedBlock.options.length > 0) ||
      (duplicatedBlock.questions && duplicatedBlock.questions.length > 0) ||
      (duplicatedBlock.headerBody &&
        (duplicatedBlock.headerBody.title || duplicatedBlock.headerBody.description));

    globalStore.completeSaveOperation(
      true,
      hasContent
        ? 'Block duplicated successfully with complete content (atomic)'
        : 'Block duplicated successfully (atomic operation)',
    );

    // 13. Refresh the entire assessment to ensure consistency
    try {
      await refreshAssessmentData(duplicatedBlock.id);
    } catch (refreshError) {
      console.error('Refresh error occurred:', refreshError);
    }

    // 14. Clean up state after DOM is stable
    setTimeout(() => {
      fabPositionLock.value = false;
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      blockCreatorStore.selectedBlockId = `block-${duplicatedBlock.id}`;
    }, 300); // Reduced timeout since no race condition
  } catch (error) {
    console.error('❌ Atomic duplication error:', error);
    globalStore.completeSaveOperation(false, 'Error duplicating block');
  } finally {
    isCreatingBlock.value = false;
    if (blockCreationInProgress.value) {
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      fabPositionLock.value = false;
    }
  }
};

// Helper function for scrolling to target
const scrollToTarget = () => {
  blockCreatorStore.scrollToTarget();
};

// Helper function for setting FAB position and scrolling
const setFabAndScroll = async (blockId: number) => {
  blockCreatorStore.setFabPosition(blockId, false);
  await nextTick();
  scrollToTarget();
};

const onClickDeleteBlock = async (item: ItemBlock, index: number) => {
  // Enhanced pre-deletion validation
  if (!item.id) {
    return;
  }

  if (!item.assessmentId) {
    return;
  }

  // Determine appropriate deletion message based on block type
  const getDeleteMessage = (blockType: string): string => {
    switch (blockType) {
      case 'HEADER':
        return 'Deleting header...';
      case 'IMAGE':
        return 'Deleting image...';
      default:
        return 'Deleting question...';
    }
  };

  // Determine appropriate success message based on block type
  const getSuccessMessage = (blockType: string): string => {
    switch (blockType) {
      case 'HEADER':
        return 'Header deleted successfully';
      case 'IMAGE':
        return 'Image deleted successfully';
      default:
        return 'Question deleted successfully';
    }
  };

  // Determine appropriate error message based on block type
  const getErrorMessage = (blockType: string): string => {
    switch (blockType) {
      case 'HEADER':
        return 'Failed to delete header';
      case 'IMAGE':
        return 'Failed to delete image';
      default:
        return 'Failed to delete question';
    }
  };

  try {
    // Enhanced validation for header blocks
    if (item.type === 'HEADER' && !item.headerBody) {
      return;
    }

    // Enhanced validation using block creator store
    if (props.type === 'evaluate') {
      const deletionValidation = blockCreatorStore.validateBlockDeletion(item.id);

      if (!deletionValidation.canDelete) {
        return;
      }
    }

    // Start save operation indicator with appropriate message
    globalStore.startSaveOperation(getDeleteMessage(item.type));

    // Delete the ItemBlock (this will cascade delete related entities)
    const deletedBlock = await assessmentService.value.deleteBlock(item);

    if (deletedBlock !== undefined) {
      // Complete save operation successfully
      globalStore.completeSaveOperation(true, getSuccessMessage(item.type));

      // Perform UI cleanup
      await handleBlockDeletionCleanup(item, index);
    } else {
      // Handle case where deletion didn't return expected result
      globalStore.completeSaveOperation(false, getErrorMessage(item.type));
    }
  } catch {
    // Complete save operation with error
    globalStore.completeSaveOperation(false, getErrorMessage(item.type));
  }
};

const handleBlockDeletionCleanup = async (_item: ItemBlock, index: number) => {
  try {
    const targetIndex = index > 0 ? index - 1 : 0;
    let focusBlockId = null;

    if (blockCreatorStore.blocks.length > 0) {
      const safeTargetIndex = Math.min(targetIndex, blockCreatorStore.blocks.length - 1);
      const targetBlock = blockCreatorStore.blocks[safeTargetIndex];

      if (targetBlock) {
        focusBlockId = targetBlock.id;
      }
    }

    if (
      (props.type === 'evaluate' && blockCreatorStore.currentAssessment?.id) ||
      (props.type === 'quiz' && props.assessmentId)
    ) {
      await refreshAssessmentData(focusBlockId);
    } else {
      await blockCreatorStore.deleteBlock(index);
      await blockCreatorStore.forceRefreshBlocks(); // ✅ refresh DOM และเลข section

      if (focusBlockId && blockCreatorStore.blocks.length > 0) {
        blockCreatorStore.selectedBlockId = `block-${focusBlockId}`;
        await setFabAndScroll(focusBlockId);
      } else {
        blockCreatorStore.selectedBlockId = undefined;
      }
    }
  } catch {
    // silent fail
  }
};

const handleAddImageBlock = async (
  index: number,
  payload: { callback: (id: number | null) => void },
) => {
  // Prevent multiple simultaneous block creation
  if (isCreatingBlock.value) {
    payload.callback(null);
    return;
  }

  try {
    isCreatingBlock.value = true;
    blockCreationInProgress.value = true;

    // Start save operation indicator
    globalStore.startSaveOperation('Creating image block...');

    const assessmentId = props.assessmentId || blockCreatorStore.getAssessmentId();
    if (!assessmentId) {
      globalStore.completeSaveOperation(false, 'Assessment ID not found');
      payload.callback(null);
      return;
    }

    const currentSection = getCurrentSection(blockCreatorStore.blocks, index);

    // Get the current block's sequence and insert the new block right after it
    const currentBlock = blockCreatorStore.blocks[index];
    const newSequence = currentBlock ? currentBlock.sequence + 1 : index + 1;

    const newImageData = {
      assessmentId: assessmentId,
      sequence: newSequence,
      section: currentSection,
      type: 'IMAGE' as const,
      isRequired: false,
    };

    console.log('call createBlock');
    // Call backend API to create the image block (without image data)
    const addedBlock = await assessmentService.value.createBlock(newImageData);

    if (addedBlock) {
      // CRITICAL: Set target block ID to allow only this block to receive focus
      targetBlockId.value = addedBlock.id;

      // CRITICAL: Lock FAB position BEFORE any DOM updates to prevent jumping
      fabPositionLock.value = true;
      blockCreatorStore.selectedBlockId = `block-${addedBlock.id}`;

      // Use the new sequence management function to properly handle insertion
      blockCreatorStore.insertBlockWithSequenceManagement(addedBlock, index);

      // CRITICAL: Sync sequences with backend to ensure persistence
      await blockCreatorStore.syncSequencesToBackend(props.type);

      // Update the evaluate form store if this is an evaluate type
      if (props.type === 'evaluate' && blockCreatorStore.currentAssessment) {
        const currentBlocks = blockCreatorStore.currentAssessment.itemBlocks || [];
        const currentBlock = currentBlocks[index];

        if (currentBlock) {
          // Update sequences of blocks that come after the insertion point
          currentBlocks.forEach((block) => {
            if (block.sequence >= addedBlock.sequence) {
              block.sequence = block.sequence + 1;
            }
          });
        }

        // Insert at correct position (index + 1)
        const newAssessmentBlocks = [
          ...currentBlocks.slice(0, index + 1),
          addedBlock,
          ...currentBlocks.slice(index + 1),
        ];

        blockCreatorStore.currentAssessment.itemBlocks = newAssessmentBlocks;
      }

      // Complete save operation successfully
      globalStore.completeSaveOperation(true, 'Image block created successfully');

      // Set FAB position and scroll (FAB is already locked to correct position)
      await setFabAndScroll(addedBlock.id);

      // Release locks after DOM is stable
      setTimeout(() => {
        fabPositionLock.value = false;
        blockCreationInProgress.value = false;
        targetBlockId.value = null;
        // Final confirmation of FAB position
        blockCreatorStore.selectedBlockId = `block-${addedBlock.id}`;
      }, 800);

      // Return the created block ID to the callback
      payload.callback(addedBlock.id);
    } else {
      globalStore.completeSaveOperation(false, 'Failed to create image block');
      payload.callback(null);
    }
  } catch {
    globalStore.completeSaveOperation(false, 'Error creating image block');
    payload.callback(null);
  } finally {
    isCreatingBlock.value = false;
    // Clean up state in case of errors
    if (blockCreationInProgress.value) {
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      fabPositionLock.value = false;
    }
  }
};

const handleImageUploaded = async () => {
  // This function is called when the image upload is completed
  // We need to refresh the current assessment data to get the updated imageBody

  try {
    // CRITICAL: Preserve the current FAB position before refreshing data
    const currentSelectedBlockId = blockCreatorStore.selectedBlockId;
    const currentBlockId = currentSelectedBlockId
      ? Number(currentSelectedBlockId.split('-')[1])
      : null;

    // CRITICAL: Lock FAB position to prevent unwanted changes during refresh
    fabPositionLock.value = true;
    blockCreationInProgress.value = true;
    if (currentBlockId) {
      targetBlockId.value = currentBlockId;
    }

    // Use our centralized refresh function
    await refreshAssessmentData(currentBlockId);

    // CRITICAL: Restore FAB position to the ImageBlock that was being worked on
    if (currentSelectedBlockId && currentBlockId) {
      // Verify the block still exists
      const blockStillExists = blockCreatorStore.blocks.some(
        (block) => block.id === currentBlockId,
      );

      if (blockStillExists) {
        blockCreatorStore.selectedBlockId = currentSelectedBlockId;

        // Scroll to the restored position
        await nextTick();
        await nextTick();
        scrollToTarget();
      }
    }

    // Release FAB locks after refresh is complete
    setTimeout(() => {
      fabPositionLock.value = false;
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
    }, 300);
  } catch (error) {
    console.error('An error occurred while refreshing assessment data:', error);
    globalStore.completeSaveOperation(false, 'Failed to refresh image data');
  }
};
// Handle image dimension updates from ImageBlock
const handleImageUpdate = (updateData: {
  itemBlockId: number;
  dimensions: { width: number; height: number };
}) => {
  console.log('🎯 Image dimensions updated, updating local data...', {
    itemBlockId: updateData.itemBlockId,
    dimensions: updateData.dimensions,
  });

  try {
    // Find the block in the local store and update dimensions directly
    const blockIndex = blockCreatorStore.blocks.findIndex(
      (block) => block.id === updateData.itemBlockId,
    );

    if (blockIndex !== -1) {
      const block = blockCreatorStore.blocks[blockIndex];
      if (block && block.imageBody) {
        // Update the dimensions in the local block data while preserving all other imageBody properties
        const updatedImageBody = {
          ...block.imageBody,
          imageWidth: updateData.dimensions.width,
          imageHeight: updateData.dimensions.height,
        };

        const updatedBlock = {
          ...block,
          imageBody: updatedImageBody,
        };

        console.log('🔒 Preserving imageBody data during dimension update:', {
          originalImageBody: block.imageBody,
          updatedImageBody: updatedImageBody,
          preservedImagePath: updatedImageBody.imagePath,
          preservedImageText: updatedImageBody.imageText,
        });

        // Update the block in the local store
        blockCreatorStore.updateBlock(updatedBlock, blockIndex);

        // Also update the assessment store if this is an evaluate type
        if (props.type === 'evaluate' && blockCreatorStore.currentAssessment?.itemBlocks) {
          const assessmentBlockIndex = blockCreatorStore.currentAssessment.itemBlocks.findIndex(
            (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
          );
          if (assessmentBlockIndex !== -1) {
            const assessmentBlock =
              blockCreatorStore.currentAssessment.itemBlocks[assessmentBlockIndex];
            if (assessmentBlock && assessmentBlock.imageBody) {
              console.log('🔒 Preserving assessment imageBody data during dimension update:', {
                originalAssessmentImageBody: assessmentBlock.imageBody,
                preservedImagePath: assessmentBlock.imageBody.imagePath,
                preservedImageText: assessmentBlock.imageBody.imageText,
              });

              // Update dimensions in the assessment store while preserving all other properties
              assessmentBlock.imageBody.imageWidth = updateData.dimensions.width;
              assessmentBlock.imageBody.imageHeight = updateData.dimensions.height;

              // Trigger reactivity
              blockCreatorStore.currentAssessment.itemBlocks = [
                ...blockCreatorStore.currentAssessment.itemBlocks,
              ];
            }
          }
        }

        console.log('✅ Store image dimensions updated successfully', {
          itemBlockId: updateData.itemBlockId,
          newDimensions: updateData.dimensions,
          updatedBlock: updatedBlock.imageBody,
        });
      } else {
        console.warn('⚠️ ImageBody not found for block:', updateData.itemBlockId);
      }
    } else {
      console.warn('⚠️ Block not found for dimension update:', updateData.itemBlockId);
    }
  } catch (error) {
    console.error('❌ Failed to update local image dimensions:', error);
  }
};

// Drag and drop event handlers
const onDragStart = () => {
  isDragging.value = true;
};

const onDragEnd = () => {
  isDragging.value = false;
};

const onDragChange = async () => {
  // Handle drag change events and sync with backend

  try {
    // Only sync when we have blocks
    if (blockCreatorStore.blocks.length > 0) {
      // Start save operation indicator
      globalStore.startSaveOperation('Updating order...');

      // Get current blocks with updated sequences, filtering out invalid blocks
      const validBlocks = blockCreatorStore.blocks.filter((block) => {
        const isValid = block.id && !isNaN(Number(block.id)) && Number(block.id) > 0;
        if (!isValid) {
          // Add logic to handle invalid blocks if necessary
        }
        return isValid;
      });

      if (validBlocks.length === 0) {
        globalStore.completeSaveOperation(false, 'No valid blocks to update');
        return;
      }

      const blocksToUpdate = validBlocks.map((block, index) => ({
        ...block,
        sequence: index + 1, // Ensure sequence matches current order
      }));

      // Call backend API to update sequences
      const result = await assessmentService.value.updateBlockSequences(blocksToUpdate);

      if (result?.success) {
        // Refresh the assessment data after updating order using our centralized function
        await refreshAssessmentData();

        // Complete save operation successfully
        globalStore.completeSaveOperation(true, 'Order updated successfully');
      } else {
        // Handle case where API call didn't return expected result
        globalStore.completeSaveOperation(false, 'Failed to update order');
      }
    }
  } catch (error) {
    console.error('An error occurred while updating order:', error);
    globalStore.completeSaveOperation(false, 'Error updating order');
  }
};
// Optimized watcher with debouncing for better performance
watch(
  () => blockCreatorStore.selectedBlockId,
  (newValue) => {
    // During block creation, aggressively enforce the target position
    if (blockCreationInProgress.value && targetBlockId.value) {
      const expectedId = `block-${targetBlockId.value}`;
      if (newValue !== expectedId) {
        // Use nextTick to avoid infinite loops
        void nextTick(() => {
          blockCreatorStore.selectedBlockId = expectedId;
        });
        return;
      }
    }

    // Debounced scroll behavior for better performance
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }
    scrollTimeout = setTimeout(() => {
      scrollToTarget();
    }, 16);
  },
  { flush: 'post' }, // Run after DOM updates for better performance
);

const getQuestionNumber = (index: number): number => {
  return (
    blockCreatorStore.blocks
      .slice(0, index)
      .filter((block) => block.type !== 'HEADER' && block.type !== 'IMAGE').length + 1
  );
};
</script>

<style scoped>
.fixed-fab-col {
  width: 48px;
}

.section-container {
  position: relative;
  z-index: 1;
  margin-bottom: 0;
}

.section-tab {
  background-color: #673ab7;
  color: white;
  font-weight: 500;
  padding: 6px 16px;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  font-size: 14px;
  width: fit-content;
  position: relative;
  left: 0;
}

.no-top-left-radius {
  border-top-left-radius: 0 !important;
}

/* Drag and Drop Styles */
.draggable-item {
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.draggable-item.is-dragging {
  opacity: 0.8;
}

.block-container {
  position: relative;
  width: 100%;
}

.block-content {
  flex: 1;
}

/* Drag states */
.ghost {
  opacity: 0.5;
  background: #f0f0f0;
  border: 2px dashed #ccc;
}

.chosen {
  opacity: 0.8;
}

.drag {
  opacity: 0.5;
  transform: rotate(5deg);
}

/* Flip animation for smooth transitions */
.flip-list-move,
.flip-list-enter-active,
.flip-list-leave-active {
  transition: all 0.3s ease;
}

.flip-list-enter-from,
.flip-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.flip-list-leave-active {
  position: absolute;
}
</style>
